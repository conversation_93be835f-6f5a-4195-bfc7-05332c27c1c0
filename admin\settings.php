<?php
session_start();
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}
require_once '../includes/db.php';
// For demonstration, just a placeholder for settings
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Settings - GreenHarvest Admin</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body { background: #e8f5e9; }
        .main-green { background: #a5d6a7 !important; color: #1b5e20 !important; }
        .card-crop { border: 1px solid #c8e6c9; border-radius: 12px; box-shadow: 0 2px 8px rgba(60,60,60,0.05); }
        .side-nav { min-height: 100vh; background: #a5d6a7; padding-top: 2em; }
        .side-nav a { color: #1b5e20; font-weight: 500; display: block; padding: 1em 1.5em; text-decoration: none; border-radius: 0 20px 20px 0; margin-bottom: 0.5em; }
        .side-nav a.active, .side-nav a:hover { background: #66bb6a; color: #fff; }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-2 side-nav">
                <h4 class="mb-4">🌿 Admin</h4>
                <a href="dashboard.php">Dashboard</a>
                <a href="investments.php">Approve Investments</a>
                <a href="withdrawals.php">Approve Withdrawals</a>
                <a href="packages.php">Crop Packages</a>
                <a href="users.php">Users</a>
                <a href="referrals.php">Referrals</a>
                <a href="announcements.php">Announcements</a>
                <a href="settings.php" class="active">Settings</a>
                <a href="logout.php" class="text-danger">Logout</a>
            </div>
            <div class="col-md-10 py-4">
                <h2 class="mb-4">Platform Settings</h2>
                <div class="card card-crop">
                    <div class="card-body">
                        <p>Settings page placeholder. Here you can add forms to update minimum withdrawal, platform configs, etc.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
