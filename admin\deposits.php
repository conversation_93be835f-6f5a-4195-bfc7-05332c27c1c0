<?php
session_start();
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}
require_once '../includes/db.php';
$pending = $pdo->query('SELECT d.*, u.name, u.email FROM deposits d JOIN users u ON d.user_id = u.id WHERE d.status = "pending" ORDER BY d.date_requested DESC')->fetchAll();
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $deposit_id = intval($_POST['deposit_id']);
    $action = $_POST['action'];
    $stmt = $pdo->prepare('SELECT * FROM deposits WHERE id = ?');
    $stmt->execute([$deposit_id]);
    $deposit = $stmt->fetch();
    if ($deposit && $deposit['status'] === 'pending') {
        if ($action === 'approve') {
            // Credit wallet
            $pdo->prepare('UPDATE wallets SET balance = balance + ? WHERE user_id = ?')->execute([$deposit['amount'], $deposit['user_id']]);
            $pdo->prepare('UPDATE deposits SET status = "approved", date_processed = NOW() WHERE id = ?')->execute([$deposit_id]);
        } elseif ($action === 'reject') {
            $pdo->prepare('UPDATE deposits SET status = "rejected", date_processed = NOW() WHERE id = ?')->execute([$deposit_id]);
        }
    }
    header('Location: deposits.php');
    exit;
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Approve Deposits - GreenHarvest Admin</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container py-5">
        <h2>Approve Deposits</h2>
        <div class="table-responsive">
            <table class="table table-bordered table-striped">
                <thead class="table-success">
                    <tr>
                        <th>User</th>
                        <th>Email</th>
                        <th>Amount</th>
                        <th>Reference</th>
                        <th>Date</th>
                        <th>Action</th>
                    </tr>
                </thead>
                <tbody>
                <?php foreach ($pending as $d): ?>
                    <tr>
                        <td><?php echo htmlspecialchars($d['name']); ?></td>
                        <td><?php echo htmlspecialchars($d['email']); ?></td>
                        <td>GHS <?php echo number_format($d['amount'],2); ?></td>
                        <td><?php echo htmlspecialchars($d['reference']); ?></td>
                        <td><?php echo $d['date_requested']; ?></td>
                        <td>
                            <form method="post" class="d-inline">
                                <input type="hidden" name="deposit_id" value="<?php echo $d['id']; ?>">
                                <button type="submit" name="action" value="approve" class="btn btn-success btn-sm">Approve</button>
                                <button type="submit" name="action" value="reject" class="btn btn-danger btn-sm">Reject</button>
                            </form>
                        </td>
                    </tr>
                <?php endforeach; ?>
                </tbody>
            </table>
        </div>
        <a href="dashboard.php" class="btn btn-secondary mt-3">Back</a>
    </div>
</body>
</html>
