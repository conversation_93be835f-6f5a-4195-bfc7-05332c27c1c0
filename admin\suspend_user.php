<?php
session_start();
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}
require_once '../includes/db.php';
if (!isset($_GET['id'])) {
    header('Location: users.php');
    exit;
}
$id = intval($_GET['id']);
// For demonstration, let's just delete the user (real implementation should set a status column)
$stmt = $pdo->prepare('DELETE FROM users WHERE id = ?');
$stmt->execute([$id]);
header('Location: users.php');
exit;
