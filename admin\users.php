<?php
session_start();
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}
require_once '../includes/db.php';
$users = $pdo->query('SELECT * FROM users ORDER BY created_at DESC')->fetchAll();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manage Users - GreenHarvest Admin</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body { background: #e8f5e9; }
        .main-green { background: #a5d6a7 !important; color: #1b5e20 !important; }
        .card-crop { border: 1px solid #c8e6c9; border-radius: 12px; box-shadow: 0 2px 8px rgba(60,60,60,0.05); }
        .side-nav { min-height: 100vh; background: #a5d6a7; padding-top: 2em; }
        .side-nav a { color: #1b5e20; font-weight: 500; display: block; padding: 1em 1.5em; text-decoration: none; border-radius: 0 20px 20px 0; margin-bottom: 0.5em; }
        .side-nav a.active, .side-nav a:hover { background: #66bb6a; color: #fff; }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-2 side-nav">
                <h4 class="mb-4">🌿 Admin</h4>
                <a href="dashboard.php">Dashboard</a>
                <a href="investments.php">Approve Investments</a>
                <a href="withdrawals.php">Approve Withdrawals</a>
                <a href="packages.php">Crop Packages</a>
                <a href="users.php" class="active">Users</a>
                <a href="referrals.php">Referrals</a>
                <a href="announcements.php">Announcements</a>
                <a href="settings.php">Settings</a>
                <a href="logout.php" class="text-danger">Logout</a>
            </div>
            <div class="col-md-10 py-4">
                <h2 class="mb-4">Manage Users</h2>
                <div class="table-responsive">
                    <table class="table table-bordered table-striped">
                        <thead class="table-success">
                            <tr>
                                <th>ID</th>
                                <th>Name</th>
                                <th>Email</th>
                                <th>Phone</th>
                                <th>Referral Code</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                        <?php foreach ($users as $u): ?>
                            <tr>
                                <td><?php echo $u['id']; ?></td>
                                <td><?php echo htmlspecialchars($u['name']); ?></td>
                                <td><?php echo htmlspecialchars($u['email']); ?></td>
                                <td><?php echo htmlspecialchars($u['phone']); ?></td>
                                <td><?php echo htmlspecialchars($u['referral_code']); ?></td>
                                <td><span class="badge bg-success">Active</span></td>
                                <td>
                                    <a href="view_user.php?id=<?php echo $u['id']; ?>" class="btn btn-info btn-sm">View</a>
                                    <a href="suspend_user.php?id=<?php echo $u['id']; ?>" class="btn btn-warning btn-sm">Suspend</a>
                                    <a href="delete_user.php?id=<?php echo $u['id']; ?>" class="btn btn-danger btn-sm" onclick="return confirm('Delete this user?');">Delete</a>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
