<?php
session_start();
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}
require_once '../includes/db.php';
$pending = $pdo->query('SELECT w.*, u.name, u.email FROM withdrawals w JOIN users u ON w.user_id = u.id WHERE w.status = "pending" ORDER BY w.date_requested DESC')->fetchAll();
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $withdrawal_id = intval($_POST['withdrawal_id']);
    $action = $_POST['action'];
    $stmt = $pdo->prepare('SELECT * FROM withdrawals WHERE id = ?');
    $stmt->execute([$withdrawal_id]);
    $withdrawal = $stmt->fetch();
    if ($withdrawal && $withdrawal['status'] === 'pending') {
        if ($action === 'approve') {
            $pdo->prepare('UPDATE withdrawals SET status = "approved", date_processed = NOW() WHERE id = ?')->execute([$withdrawal_id]);
        } elseif ($action === 'reject') {
            // Refund wallet
            $pdo->prepare('UPDATE wallets SET balance = balance + ? WHERE user_id = ?')->execute([$withdrawal['amount'], $withdrawal['user_id']]);
            $pdo->prepare('UPDATE withdrawals SET status = "rejected", date_processed = NOW() WHERE id = ?')->execute([$withdrawal_id]);
        }
    }
    header('Location: withdrawals.php');
    exit;
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Approve Withdrawals - GreenHarvest Admin</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body { background: #e8f5e9; }
        .main-green { background: #a5d6a7 !important; color: #1b5e20 !important; }
        .card-crop { border: 1px solid #c8e6c9; border-radius: 12px; box-shadow: 0 2px 8px rgba(60,60,60,0.05); }
        .side-nav { min-height: 100vh; background: #a5d6a7; padding-top: 2em; }
        .side-nav a { color: #1b5e20; font-weight: 500; display: block; padding: 1em 1.5em; text-decoration: none; border-radius: 0 20px 20px 0; margin-bottom: 0.5em; }
        .side-nav a.active, .side-nav a:hover { background: #66bb6a; color: #fff; }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-2 side-nav">
                <h4 class="mb-4">🌿 Admin</h4>
                <a href="dashboard.php">Dashboard</a>
                <a href="investments.php">Approve Investments</a>
                <a href="withdrawals.php" class="active">Approve Withdrawals</a>
                <a href="packages.php">Crop Packages</a>
                <a href="users.php">Users</a>
                <a href="referrals.php">Referrals</a>
                <a href="announcements.php">Announcements</a>
                <a href="settings.php">Settings</a>
                <a href="logout.php" class="text-danger">Logout</a>
            </div>
            <div class="col-md-10 py-4">
                <h2 class="mb-4">Approve Withdrawals</h2>
                <div class="row">
                    <?php if ($pending): foreach ($pending as $w): ?>
                        <div class="col-md-6 col-lg-4 mb-4">
                            <div class="card card-crop h-100">
                                <div class="card-body">
                                    <h5 class="card-title text-success">GHS <?php echo number_format($w['amount'],2); ?></h5>
                                    <p>User: <strong><?php echo htmlspecialchars($w['name']); ?></strong> (<?php echo htmlspecialchars($w['email']); ?>)</p>
                                    <p>Date: <?php echo htmlspecialchars($w['date_requested']); ?></p>
                                    <form method="post" class="d-flex gap-2">
                                        <input type="hidden" name="withdrawal_id" value="<?php echo $w['id']; ?>">
                                        <button type="submit" name="action" value="approve" class="btn btn-success btn-sm">Approve</button>
                                        <button type="submit" name="action" value="reject" class="btn btn-danger btn-sm">Reject</button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; else: ?>
                        <div class="col-12"><div class="alert alert-info">No pending withdrawals.</div></div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
