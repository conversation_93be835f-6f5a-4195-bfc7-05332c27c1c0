ting for with<?php
session_start();
if (!isset($_SESSION['user_id'])) {
    header('Location: ../index.php');
    exit;
}
require_once '../includes/db.php';
$user_id = $_SESSION['user_id'];
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $package_id = intval($_POST['package_id']);
    $amount = floatval($_POST['amount']);
    $method = trim($_POST['method']);
    $reference = trim($_POST['reference']);
    // Get wallet balance
    $stmt = $pdo->prepare('SELECT balance FROM wallets WHERE user_id = ?');
    $stmt->execute([$user_id]);
    $wallet = $stmt->fetch();
    $balance = $wallet ? $wallet['balance'] : 0;
    if ($balance < $amount) {
        $_SESSION['crop_error'] = 'Insufficient wallet balance.';
        header('Location: my_products.php');
        exit;
    }
    // Deduct from wallet
    $pdo->prepare('UPDATE wallets SET balance = balance - ? WHERE user_id = ?')->execute([$amount, $user_id]);
    // Get package details
    $pkg = $pdo->prepare('SELECT * FROM packages WHERE id = ?');
    $pkg->execute([$package_id]);
    $pkg = $pkg->fetch();
    if (!$pkg) {
        $_SESSION['crop_error'] = 'Invalid crop package.';
        header('Location: my_products.php');
        exit;
    }
    $start_date = date('Y-m-d');
    $end_date = date('Y-m-d', strtotime("+{$pkg['duration']} days"));
    $stmt = $pdo->prepare('INSERT INTO investments (user_id, package_id, start_date, end_date, daily_return, status, payment_reference) VALUES (?, ?, ?, ?, ?, "active", ?)');
    $stmt->execute([$user_id, $package_id, $start_date, $end_date, $pkg['daily_return'], $reference]);
    $_SESSION['crop_success'] = 'Crop investment started successfully!';
    header('Location: my_products.php');
    exit;
}
header('Location: my_products.php');
exit;
