<?php
session_start();
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}
require_once '../includes/db.php';
$error = '';
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $title = trim($_POST['title']);
    $message = trim($_POST['message']);
    if ($title && $message) {
        $stmt = $pdo->prepare('INSERT INTO announcements (title, message) VALUES (?, ?)');
        $stmt->execute([$title, $message]);
    } else {
        $error = 'Please fill in all fields.';
    }
}
if (isset($_GET['delete'])) {
    $id = intval($_GET['delete']);
    $pdo->prepare('DELETE FROM announcements WHERE id = ?')->execute([$id]);
    header('Location: announcements.php');
    exit;
}
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['edit_id'])) {
    $id = intval($_POST['edit_id']);
    $title = trim($_POST['edit_title']);
    $message = trim($_POST['edit_message']);
    if ($title && $message) {
        $pdo->prepare('UPDATE announcements SET title = ?, message = ? WHERE id = ?')->execute([$title, $message, $id]);
    }
    header('Location: announcements.php');
    exit;
}
$announcements = $pdo->query('SELECT * FROM announcements ORDER BY date_posted DESC')->fetchAll();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Announcements - GreenHarvest Admin</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body { background: #e8f5e9; }
        .main-green { background: #a5d6a7 !important; color: #1b5e20 !important; }
        .card-crop { border: 1px solid #c8e6c9; border-radius: 12px; box-shadow: 0 2px 8px rgba(60,60,60,0.05); }
        .side-nav { min-height: 100vh; background: #a5d6a7; padding-top: 2em; }
        .side-nav a { color: #1b5e20; font-weight: 500; display: block; padding: 1em 1.5em; text-decoration: none; border-radius: 0 20px 20px 0; margin-bottom: 0.5em; }
        .side-nav a.active, .side-nav a:hover { background: #66bb6a; color: #fff; }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-2 side-nav">
                <h4 class="mb-4">🌿 Admin</h4>
                <a href="dashboard.php">Dashboard</a>
                <a href="investments.php">Approve Investments</a>
                <a href="withdrawals.php">Approve Withdrawals</a>
                <a href="packages.php">Crop Packages</a>
                <a href="users.php">Users</a>
                <a href="referrals.php">Referrals</a>
                <a href="announcements.php" class="active">Announcements</a>
                <a href="settings.php">Settings</a>
                <a href="logout.php" class="text-danger">Logout</a>
            </div>
            <div class="col-md-10 py-4">
                <h2 class="mb-4">Announcements</h2>
                <?php if ($error): ?><div class="alert alert-danger"><?php echo $error; ?></div><?php endif; ?>
                <form method="post" class="mb-4">
                    <div class="row g-2">
                        <div class="col-md-4">
                            <input type="text" name="title" class="form-control" placeholder="Title" required>
                        </div>
                        <div class="col-md-6">
                            <input type="text" name="message" class="form-control" placeholder="Message" required>
                        </div>
                        <div class="col-md-2">
                            <button type="submit" class="btn btn-success w-100">Post</button>
                        </div>
                    </div>
                </form>
                <div class="card card-crop">
                    <div class="card-body">
                        <h5>Recent Announcements</h5>
                        <ul class="list-group list-group-flush">
                            <?php foreach ($announcements as $a): ?>
                                <li class="list-group-item">
                                    <form method="post" class="d-inline-block w-100" style="max-width:100%;">
                                        <input type="hidden" name="edit_id" value="<?php echo $a['id']; ?>">
                                        <div class="row g-2 align-items-center">
                                            <div class="col-md-3">
                                                <input type="text" name="edit_title" class="form-control form-control-sm" value="<?php echo htmlspecialchars($a['title']); ?>" required>
                                            </div>
                                            <div class="col-md-5">
                                                <input type="text" name="edit_message" class="form-control form-control-sm" value="<?php echo htmlspecialchars($a['message']); ?>" required>
                                            </div>
                                            <div class="col-md-2">
                                                <span class="text-muted" style="font-size:0.9em;">(<?php echo date('M d, Y', strtotime($a['date_posted'])); ?>)</span>
                                            </div>
                                            <div class="col-md-2 text-end">
                                                <button type="submit" class="btn btn-warning btn-sm">Edit</button>
                                                <a href="announcements.php?delete=<?php echo $a['id']; ?>" class="btn btn-danger btn-sm" onclick="return confirm('Delete this announcement?');">Delete</a>
                                            </div>
                                        </div>
                                    </form>
                                </li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
