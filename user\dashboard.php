<?php
session_start();
if (!isset($_SESSION['user_id'])) {
    header('Location: ../index.php');
    exit;
}
require_once '../includes/db.php';
$user_id = $_SESSION['user_id'];
// Fetch user info
$stmt = $pdo->prepare('SELECT name, email FROM users WHERE id = ?');
$stmt->execute([$user_id]);
$user = $stmt->fetch();
// Fetch wallet balance
$stmt = $pdo->prepare('SELECT balance FROM wallets WHERE user_id = ?');
$stmt->execute([$user_id]);
$wallet = $stmt->fetch();
$balance = $wallet ? $wallet['balance'] : 0;
// Fetch active crops
$stmt = $pdo->prepare('SELECT COUNT(*) FROM investments WHERE user_id = ? AND status = "active"');
$stmt->execute([$user_id]);
$active_crops = $stmt->fetchColumn();
// Fetch daily harvest earnings (today)
$stmt = $pdo->prepare('SELECT SUM(daily_return) FROM investments WHERE user_id = ? AND status = "active"');
$stmt->execute([$user_id]);
$daily_earnings = $stmt->fetchColumn() ?: 0;
// Fetch referral earnings (sum of all referral bonuses)
$stmt = $pdo->prepare('SELECT SUM(amount) FROM transactions WHERE user_id = ? AND type = "investment" AND status = "approved" AND reference LIKE "REF-L%"');
$stmt->execute([$user_id]);
$referral_earnings = $stmt->fetchColumn() ?: 0;
// Fetch announcements
$announcements = $pdo->query('SELECT * FROM announcements ORDER BY date_posted DESC LIMIT 3')->fetchAll();
// Motivational quotes
$quotes = [
    "The best time to plant a tree was 20 years ago. The second best time is now.",
    "Growth is never by mere chance; it is the result of forces working together.",
    "From tiny seeds grow mighty crops.",
    "Your harvest tomorrow depends on the seeds you plant today."
];
$quote = $quotes[array_rand($quotes)];
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - GreenHarvest</title>
    <link rel="icon" type="image/png" href="../assets/images/favicon.png">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css">
    <style>
        body { background: #e8f5e9; }
        .main-green { background: #a5d6a7 !important; color: #1b5e20 !important; }
        .secondary-green { background: #66bb6a !important; color: #fff !important; }
        .card-crop { border: 1px solid #c8e6c9; border-radius: 12px; box-shadow: 0 2px 8px rgba(60,60,60,0.05); }
        .dashboard-header { background: #43a047; color: #fff; border-radius: 12px 12px 0 0; }
        .nav-link.active { background: #388e3c !important; color: #fff !important; }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg main-green mb-4">
        <div class="container-fluid">
            <a class="navbar-brand fw-bold" href="#">🌿 GreenHarvest</a>
            <div class="d-flex">
                <a href="logout.php" class="btn btn-sm btn-danger">Logout</a>
            </div>
        </div>
    </nav>
    <div class="container">
        <!-- Carousel -->
        <div id="farmCarousel" class="carousel slide mb-4" data-bs-ride="carousel">
          <div class="carousel-inner rounded-4 shadow">
            <div class="carousel-item active">
              <img src="https://media.licdn.com/dms/image/v2/D4D12AQFdHZW1A0GRlQ/article-cover_image-shrink_720_1280/article-cover_image-shrink_720_1280/0/1738076969133?e=2147483647&v=beta&t=fjT5hiXqyNxLOd8XzRFQD4L0fvy_d1rint2lV63B3rw" class="d-block w-100" alt="Farm 1" style="height:220px;object-fit:cover;">
              <div class="carousel-caption d-md-block">
                <h5 class="bg-success bg-opacity-75 rounded px-2">Mixed Agro Farm</h5>
              </div>
            </div>
            <div class="carousel-item">
              <img src="https://news.siu.edu/_assets/images/2018/09/corn.jpg" class="d-block w-100" alt="Farm 2" style="height:220px;object-fit:cover;">
              <div class="carousel-caption d-md-block">
                <h5 class="bg-success bg-opacity-75 rounded px-2">Maize Farm</h5>
              </div>
            </div>
            <div class="carousel-item">
              <img src="https://assets.theedgemarkets.com/20240911_PLA_PALM%20OIL_DSC_8453_LYY_20240914093447_theedgemalaysia_4.jpg" class="d-block w-100" alt="Farm 3" style="height:220px;object-fit:cover;">
              <div class="carousel-caption d-md-block">
                <h5 class="bg-success bg-opacity-75 rounded px-2">Oil Palm</h5>
              </div>
            </div>
            <div class="carousel-item">
              <img src="https://www.botanicalinterests.com/community/blog/wp-content/uploads/2024/08/watermelon-sow-and-grow-guide.jpg" class="d-block w-100" alt="Farm 4" style="height:220px;object-fit:cover;">
              <div class="carousel-caption d-md-block">
                <h5 class="bg-success bg-opacity-75 rounded px-2">Watermelon Farm</h5>
              </div>
            </div>
            <div class="carousel-item">
              <img src="https://pakobserver.net/wp-content/uploads/2020/07/1-179.jpg" class="d-block w-100" alt="Farm 5" style="height:220px;object-fit:cover;">
              <div class="carousel-caption d-md-block">
                <h5 class="bg-success bg-opacity-75 rounded px-2">Rice Paddies</h5>
              </div>
            </div>
          </div>
          <button class="carousel-control-prev" type="button" data-bs-target="#farmCarousel" data-bs-slide="prev">
            <span class="carousel-control-prev-icon visually-hidden" aria-hidden="true"></span>
            <span class="visually-hidden">Previous</span>
          </button>
          <button class="carousel-control-next" type="button" data-bs-target="#farmCarousel" data-bs-slide="next">
            <span class="carousel-control-next-icon visually-hidden" aria-hidden="true"></span>
            <span class="visually-hidden">Next</span>
          </button>
        </div>
        <!-- Row: Active Crops & Daily Harvest Earnings -->
        <div class="row gx-2 align-items-stretch" style="margin-bottom: 1.5rem;">
            <div class="col-6">
                <div class="card card-crop h-100">
                    <div class="card-body d-flex flex-column justify-content-center">
                        <h6><i class="bi bi-flower1 me-1"></i>Active Crops</h6>
                        <h3 class="text-success" id="active-crops"><?php echo $active_crops; ?></h3>
                    </div>
                </div>
            </div>
            <div class="col-6">
                <div class="card card-crop h-100">
                    <div class="card-body d-flex flex-column justify-content-center">
                        <h6><i class="bi bi-cash-coin me-1"></i>Daily Harvest Earnings</h6>
                        <h3 class="text-success" id="daily-earnings">GHS <?php echo number_format($daily_earnings,2); ?></h3>
                    </div>
                </div>
            </div>
        </div>
        <!-- My Funds Card -->
        <div class="row mb-4">
            <div class="col-md-6 mb-3">
                <div class="card card-crop">
                    <div class="card-header bg-success text-center text-white"> - Wallet Balance -
                        <h2 class="text-white mb-3"><i class="bi bi-wallet2 me-1"></i>GHS <?php echo number_format($balance,2); ?></h2>
                    </div>
                    <div class="card-body text-center">
                        <a href="deposit.php" class="btn btn-success">Deposit</a>
                        <a href="profile.php" class="btn btn-outline-success">Withdraw</a>
                    </div>
                </div>
            </div>
            <div class="col-md-6 mb-3">
                <div class="card card-crop h-100">
                    <div class="card-body">
                        <h6>Motivational Quote</h6>
                        <blockquote class="blockquote mb-0">
                            <p><?php echo $quote; ?></p>
                        </blockquote>
                    </div>
                </div>
            </div>
        </div>
        <div class="row mb-4">
            <div class="col-12">
                <div class="card card-crop">
                    <div class="card-header dashboard-header">
                        <h5 class="mb-0">Announcements</h5>
                    </div>
                    <div class="card-body">
                        <?php if ($announcements): ?>
                            <ul class="list-group list-group-flush">
                                <?php foreach ($announcements as $a): ?>
                                    <li class="list-group-item">
                                        <strong><?php echo htmlspecialchars($a['title']); ?>:</strong> <?php echo htmlspecialchars($a['message']); ?>
                                        <span class="text-muted float-end" style="font-size:0.9em;">(<?php echo date('M d, Y', strtotime($a['date_posted'])); ?>)</span>
                                    </li>
                                <?php endforeach; ?>
                            </ul>
                        <?php else: ?>
                            <p>No announcements at this time.</p>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
        </div>
        <!-- Fixed Bottom Navigation -->
        <nav class="nav nav-pills nav-fill fixed-bottom bottom-nav-green shadow-lg" style="z-index:1030;">
            <a class="nav-link" href="dashboard.php"><span class="bi bi-house-door"></span> Home</a>
            <a class="nav-link" href="products.php"><span class="bi bi-flower1"></span> My Farms</a>
            <a class="nav-link" href="my_products.php"><span class="bi bi-bag"></span> Crops</a>
            <a class="nav-link" href="team.php"><span class="bi bi-people"></span> Network</a>
            <a class="nav-link" href="profile.php"><span class="bi bi-person"></span> Profile</a>
        </nav>
    </div>
    <style>
    .bottom-nav-green {
        background: #a5d6a7;
        border-top: 2px solid #66bb6a;
        padding-bottom: env(safe-area-inset-bottom, 0);
    }
    .bottom-nav-green .nav-link {
        color: #1b5e20;
        font-weight: 500;
        font-size: 1.1em;
        padding: 0.7em 0 0.5em 0;
        border-radius: 0;
        transition: background 0.2s, color 0.2s;
    }
    .bottom-nav-green .nav-link.active, .bottom-nav-green .nav-link:focus, .bottom-nav-green .nav-link:hover {
        background: #66bb6a;
        color: #fff;
    }
    .bottom-nav-green .nav-link span {
        display: block;
        font-size: 1.3em;
    }
    body { padding-bottom: 70px !important; }
    </style>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css">
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
