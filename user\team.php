<?php
session_start();
if (!isset($_SESSION['user_id'])) {
    header('Location: ../index.php');
    exit;
}
require_once '../includes/db.php';
$user_id = $_SESSION['user_id'];
// Fetch referral stats
$levels = [1 => 0, 2 => 0, 3 => 0];
$bonuses = [1 => 0, 2 => 0, 3 => 0];
for ($level = 1; $level <= 3; $level++) {
    $stmt = $pdo->prepare('SELECT COUNT(*) as cnt FROM referrals WHERE user_id = ? AND level = ?');
    $stmt->execute([$user_id, $level]);
    $levels[$level] = $stmt->fetchColumn();
    $bonus_stmt = $pdo->prepare('SELECT SUM(amount) as total FROM transactions WHERE user_id = ? AND type = "investment" AND status = "approved" AND reference LIKE ?');
    $bonus_stmt->execute([$user_id, '%REF-L'.$level.'%']);
    $bonuses[$level] = $bonus_stmt->fetchColumn() ?: 0;
}
// Get user's referral code
$stmt = $pdo->prepare('SELECT referral_code FROM users WHERE id = ?');
$stmt->execute([$user_id]);
$referral_code = $stmt->fetchColumn();
$link = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . "://" . $_SERVER['HTTP_HOST'] . "/user/register.php?ref=" . $referral_code;
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Farmer Network - GreenHarvest</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="../assets/css/style.css">
    <style>
        body { background: #e8f5e9; }
        .main-green { background: #a5d6a7 !important; color: #1b5e20 !important; }
        .btn-farm { background: #43a047; color: #fff; }
        .btn-farm:hover { background: #2e7d32; }
        .card-crop { border: 1px solid #c8e6c9; border-radius: 12px; box-shadow: 0 2px 8px rgba(60,60,60,0.05); }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg main-green mb-4">
        <div class="container-fluid">
            <a class="navbar-brand fw-bold" href="dashboard.php">🌿 GreenHarvest</a>
            <a href="logout.php" class="btn btn-sm btn-danger">Logout</a>
        </div>
    </nav>
    <div class="container">
        <h2 class="mb-4">Farmer Network (Referral Tree)</h2>
        <div class="row mb-4">
            <div class="col-md-4 mb-3">
                <div class="card card-crop">
                    <div class="card-body">
                        <h6>Level 1 Referrals <span class="badge bg-success">25%</span></h6>
                        <h3 class="text-success"><?php echo $levels[1]; ?></h3>
                        <p>Bonus: <strong>GHS <?php echo number_format($bonuses[1],2); ?></strong></p>
                    </div>
                </div>
            </div>
            <div class="col-md-4 mb-3">
                <div class="card card-crop">
                    <div class="card-body">
                        <h6>Level 2 Referrals <span class="badge bg-success">15%</span></h6>
                        <h3 class="text-success"><?php echo $levels[2]; ?></h3>
                        <p>Bonus: <strong>GHS <?php echo number_format($bonuses[2],2); ?></strong></p>
                    </div>
                </div>
            </div>
            <div class="col-md-4 mb-3">
                <div class="card card-crop">
                    <div class="card-body">
                        <h6>Level 3 Referrals <span class="badge bg-success">5%</span></h6>
                        <h3 class="text-success"><?php echo $levels[3]; ?></h3>
                        <p>Bonus: <strong>GHS <?php echo number_format($bonuses[3],2); ?></strong></p>
                    </div>
                </div>
            </div>
        </div>
        <div class="row mb-4">
            <div class="col-12">
                <div class="card card-crop">
                    <div class="card-body">
                        <h6>Your Unique Referral Link</h6>
                        <div class="input-group">
                            <input type="text" class="form-control" value="<?php echo htmlspecialchars($link); ?>" readonly>
                            <button class="btn btn-farm" onclick="navigator.clipboard.writeText('<?php echo htmlspecialchars($link); ?>')">Copy</button>
                        </div>
                        <small class="text-muted">Share this link to grow your Farmer Network and earn bonuses!</small>
                    </div>
                </div>
            </div>
        </div>
        </div>
        <!-- Fixed Bottom Navigation -->
        <nav class="nav nav-pills nav-fill fixed-bottom bottom-nav-green shadow-lg" style="z-index:1030;">
            <a class="nav-link" href="dashboard.php"><span class="bi bi-house-door"></span> Home</a>
            <a class="nav-link" href="products.php"><span class="bi bi-flower1"></span> My Crops</a>
            <a class="nav-link" href="my_products.php"><span class="bi bi-bag"></span> Crops</a>
            <a class="nav-link active" href="team.php"><span class="bi bi-people"></span> Network</a>
            <a class="nav-link" href="profile.php"><span class="bi bi-person"></span> Profile</a>
        </nav>
    </div>
    <style>
    .bottom-nav-green {
        background: #a5d6a7;
        border-top: 2px solid #66bb6a;
        padding-bottom: env(safe-area-inset-bottom, 0);
    }
    .bottom-nav-green .nav-link {
        color: #1b5e20;
        font-weight: 500;
        font-size: 1.1em;
        padding: 0.7em 0 0.5em 0;
        border-radius: 0;
        transition: background 0.2s, color 0.2s;
    }
    .bottom-nav-green .nav-link.active, .bottom-nav-green .nav-link:focus, .bottom-nav-green .nav-link:hover {
        background: #66bb6a;
        color: #fff;
    }
    .bottom-nav-green .nav-link span {
        display: block;
        font-size: 1.3em;
    }
    body { padding-bottom: 70px !important; }
    </style>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css">
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
