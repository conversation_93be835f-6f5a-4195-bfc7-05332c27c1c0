<?php
session_start();
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}
require_once '../includes/db.php';
$packages = $pdo->query('SELECT * FROM packages ORDER BY amount ASC')->fetchAll();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Crop Packages - GreenHarvest Admin</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body { background: #e8f5e9; }
        .main-green { background: #a5d6a7 !important; color: #1b5e20 !important; }
        .card-crop { border: 1px solid #c8e6c9; border-radius: 12px; box-shadow: 0 2px 8px rgba(60,60,60,0.05); }
        .side-nav { min-height: 100vh; background: #a5d6a7; padding-top: 2em; }
        .side-nav a { color: #1b5e20; font-weight: 500; display: block; padding: 1em 1.5em; text-decoration: none; border-radius: 0 20px 20px 0; margin-bottom: 0.5em; }
        .side-nav a.active, .side-nav a:hover { background: #66bb6a; color: #fff; }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-2 side-nav">
                <h4 class="mb-4">🌿 Admin</h4>
                <a href="dashboard.php">Dashboard</a>
                <a href="investments.php">Approve Investments</a>
                <a href="withdrawals.php">Approve Withdrawals</a>
                <a href="packages.php" class="active">Crop Packages</a>
                <a href="users.php">Users</a>
                <a href="referrals.php">Referrals</a>
                <a href="announcements.php">Announcements</a>
                <a href="settings.php">Settings</a>
                <a href="logout.php" class="text-danger">Logout</a>
            </div>
            <div class="col-md-10 py-4">
                <h2 class="mb-4">Crop Packages</h2>
                <a href="add_package.php" class="btn btn-success mb-3">Add New Package</a>
                <div class="row">
                    <?php if ($packages): foreach ($packages as $pkg): ?>
                        <div class="col-md-6 col-lg-4 mb-4">
                            <div class="card card-crop h-100">
                                <div class="card-body">
                                    <?php if ($pkg['image']): ?>
                        <img src="../<?php echo $pkg['image']; ?>" class="card-img-top" alt="<?php echo htmlspecialchars($pkg['crop_name']); ?>" style="max-height:160px;object-fit:cover;">
                        <?php endif; ?>
                                    <h5 class="card-title text-success"><?php echo htmlspecialchars($pkg['crop_name']); ?></h5>
                                    <p><?php echo htmlspecialchars($pkg['description']); ?></p>
                                    <ul class="list-unstyled mb-3">
                                        <li><strong>Investment:</strong> GHS <?php echo number_format($pkg['amount'],2); ?></li>
                                        <li><strong>Daily Return:</strong> GHS <?php echo number_format($pkg['daily_return'],2); ?></li>
                                        <li><strong>Duration:</strong> <?php echo $pkg['duration']; ?> days</li>
                                        <li><strong>Total ROI:</strong> GHS <?php echo number_format($pkg['total_return'],2); ?></li>
                                    </ul>
                                    <a href="edit_package.php?id=<?php echo $pkg['id']; ?>" class="btn btn-warning btn-sm">Edit</a>
                                    <a href="delete_package.php?id=<?php echo $pkg['id']; ?>" class="btn btn-danger btn-sm" onclick="return confirm('Delete this package?');">Delete</a>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; else: ?>
                        <div class="col-12"><div class="alert alert-info">No packages found.</div></div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
