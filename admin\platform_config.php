<?php
session_start();
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}
require_once '../includes/db.php';
// Get current payment number
$stmt = $pdo->prepare("SELECT config_value FROM platform_config WHERE config_key = 'payment_number'");
$stmt->execute();
$payment_number = $stmt->fetchColumn();
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $new_number = trim($_POST['payment_number']);
    if ($payment_number) {
        $stmt = $pdo->prepare("UPDATE platform_config SET config_value = ? WHERE config_key = 'payment_number'");
        $stmt->execute([$new_number]);
    } else {
        $stmt = $pdo->prepare("INSERT INTO platform_config (config_key, config_value) VALUES ('payment_number', ?)");
        $stmt->execute([$new_number]);
    }
    header('Location: platform_config.php');
    exit;
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Platform Config - GreenHarvest Admin</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container py-5">
        <h2>Platform Configurations</h2>
        <form method="post" class="mb-4">
            <div class="mb-3">
                <label for="payment_number" class="form-label">Current Payment Number</label>
                <input type="text" class="form-control" id="payment_number" name="payment_number" value="<?php echo htmlspecialchars($payment_number); ?>" required>
            </div>
            <button type="submit" class="btn btn-success">Update Number</button>
            <a href="dashboard.php" class="btn btn-secondary">Back</a>
        </form>
    </div>
</body>
</html>
