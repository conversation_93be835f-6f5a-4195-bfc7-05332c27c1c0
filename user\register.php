<?php
session_start();
require_once '../includes/db.php';

$error = '';
$success = '';
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $name = trim($_POST['name']);
    $email = trim($_POST['email']);
    $phone = trim($_POST['phone']);
    $password = $_POST['password'];
    $confirm = $_POST['confirm'];
    $referral = trim($_POST['referral']);
    if ($name && $email && $phone && $password && $confirm) {
        if ($password !== $confirm) {
            $error = 'Passwords do not match.';
        } else {
            $stmt = $pdo->prepare('SELECT id FROM users WHERE email = ?');
            $stmt->execute([$email]);
            if ($stmt->fetch()) {
                $error = 'Email already registered.';
            } else {
                $referral_code = substr(md5(uniqid(rand(), true)), 0, 8);
                $hashed = password_hash($password, PASSWORD_DEFAULT);
                $referred_by = $referral ? $referral : null;
                $stmt = $pdo->prepare('INSERT INTO users (name, email, phone, password, referral_code, referred_by) VALUES (?, ?, ?, ?, ?, ?)');
                $stmt->execute([$name, $email, $phone, $hashed, $referral_code, $referred_by]);
                $user_id = $pdo->lastInsertId();
                $pdo->prepare('INSERT INTO wallets (user_id, balance) VALUES (?, 0)')->execute([$user_id]);
                $success = 'Registration successful! You can now login.';
            }
        }
    } else {
        $error = 'Please fill in all fields.';
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Register - GreenHarvest</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="../assets/css/style.css">
    <style>
        body {
            background: #e8f5e9;
        }
        .main-green {
            background: #a5d6a7 !important;
            color: #1b5e20 !important;
        }
        .btn-farm {
            background: #43a047;
            color: #fff;
        }
        .btn-farm:hover {
            background: #2e7d32;
        }
        .card-crop {
            border: 1px solid #c8e6c9;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(60,60,60,0.05);
        }
    </style>
</head>
<body>
    <div class="container py-5">
        <div class="row justify-content-center">
            <div class="col-md-6">
                <div class="card card-crop p-4 main-green">
                    <h2 class="mb-3 text-center">Register</h2>
                    <?php if ($error): ?>
                        <div class="alert alert-danger"><?php echo $error; ?></div>
                    <?php elseif ($success): ?>
                        <div class="alert alert-success"><?php echo $success; ?></div>
                    <?php endif; ?>
                    <form method="post" autocomplete="off">
                        <div class="mb-3">
                            <label for="name" class="form-label">Full Name</label>
                            <input type="text" class="form-control" id="name" name="name" required>
                        </div>
                        <div class="mb-3">
                            <label for="email" class="form-label">Email</label>
                            <input type="email" class="form-control" id="email" name="email" required>
                        </div>
                        <div class="mb-3">
                            <label for="phone" class="form-label">Phone Number</label>
                            <input type="text" class="form-control" id="phone" name="phone" required>
                        </div>
                        <div class="mb-3">
                            <label for="password" class="form-label">Password</label>
                            <input type="password" class="form-control" id="password" name="password" required>
                        </div>
                        <div class="mb-3">
                            <label for="confirm" class="form-label">Confirm Password</label>
                            <input type="password" class="form-control" id="confirm" name="confirm" required>
                        </div>
                        <div class="mb-3">
                            <label for="referral" class="form-label">Referral Code (optional)</label>
                            <input type="text" class="form-control" id="referral" name="referral">
                        </div>
                        <button type="submit" class="btn btn-farm w-100">Register</button>
                    </form>
                    <div class="mt-3 text-center">
                        <a href="../index.php" class="text-success">Already have an account? Login</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
