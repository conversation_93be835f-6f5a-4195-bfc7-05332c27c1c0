<?php
session_start();
if (!isset($_SESSION['user_id'])) {
    header('Location: ../index.php');
    exit;
}
require_once '../includes/db.php';
// Fetch all available crop packages
$packages = $pdo->query('SELECT * FROM packages ORDER BY amount ASC')->fetchAll();

// Fetch user wallet balance
$user_id = $_SESSION['user_id'];
$stmt = $pdo->prepare('SELECT balance FROM wallets WHERE user_id = ?');
$stmt->execute([$user_id]);
$wallet = $stmt->fetch();
$balance = $wallet ? $wallet['balance'] : 0;
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Available Crops - GreenHarvest</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="../assets/css/style.css">
    <style>
        body { background: #e8f5e9; }
        .main-green { background: #a5d6a7 !important; color: #1b5e20 !important; }
        .btn-farm { background: #43a047; color: #fff; }
        .btn-farm:hover { background: #2e7d32; }
        .card-crop { border: 1px solid #c8e6c9; border-radius: 12px; box-shadow: 0 2px 8px rgba(60,60,60,0.05); }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg main-green mb-4">
        <div class="container-fluid">
            <a class="navbar-brand fw-bold" href="dashboard.php">🌿 GreenHarvest</a>
            <a href="logout.php" class="btn btn-sm btn-danger">Logout</a>
        </div>
    </nav>
    <div class="container">
        <h2 class="mb-4">Available Crop Investment Packages</h2>
        <div class="row mb-3">
            
            <?php foreach ($packages as $pkg): ?>
                <div class="col-md-6 col-lg-4 h-50 mb-4">
                    <div class="card card-crop h-100">
                        <?php if ($pkg['image']): ?>
                        <img src="../<?php echo $pkg['image']; ?>" class="card-img-top" alt="<?php echo htmlspecialchars($pkg['crop_name']); ?>" style="max-height:160px;object-fit:cover;">
                        <?php endif; ?>
                        <div class="card-body">
                            <h5 class="card-title text-success"><?php echo htmlspecialchars($pkg['crop_name']); ?></h5>
                            <p class="card-text"><?php echo htmlspecialchars($pkg['description']); ?></p>
                            <ul class="list-unstyled mb-3">
                                <li><strong>Investment:</strong> GHS <?php echo number_format($pkg['amount'],2); ?></li>
                                <li><strong>Daily Return:</strong> GHS <?php echo number_format($pkg['daily_return'],2); ?></li>
                                <li><strong>Duration:</strong> <?php echo $pkg['duration']; ?> days</li>
                                <li><strong>Total ROI:</strong> GHS <?php echo number_format($pkg['total_return'],2); ?></li>
                            </ul>
                            <?php if ($balance >= $pkg['amount']): ?>
                                <button class="btn btn-farm w-100" data-bs-toggle="modal" data-bs-target="#startFarmModal<?php echo $pkg['id']; ?>">Start Farming</button>
                            <?php else: ?>
                                <button class="btn btn-secondary w-100" disabled>Insufficient Balance</button>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                <!-- Modal for Start Farming -->
                <div class="modal fade" id="startFarmModal<?php echo $pkg['id']; ?>" tabindex="-1" aria-labelledby="startFarmLabel<?php echo $pkg['id']; ?>" aria-hidden="true">
                  <div class="modal-dialog">
                    <div class="modal-content">
                      <form method="post" action="start_farming.php">
                        <div class="modal-header">
                          <h5 class="modal-title" id="startFarmLabel<?php echo $pkg['id']; ?>">Start Farming: <?php echo htmlspecialchars($pkg['crop_name']); ?></h5>
                          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                          <input type="hidden" name="package_id" value="<?php echo $pkg['id']; ?>">
                          <input type="hidden" name="amount" value="<?php echo $pkg['amount']; ?>">
                          <div class="mb-3">
                            <label class="form-label">Amount to Invest</label>
                            <input type="text" class="form-control" value="GHS <?php echo number_format($pkg['amount'],2); ?>" readonly>
                          </div>
                          <div class="alert alert-info">This amount will be deducted from your wallet. Are you sure you want to start farming?</div>
                        </div>
                        <div class="modal-footer">
                          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                          <button type="submit" class="btn btn-farm">Start Farming</button>
                        </div>
                      </form>
                    </div>
                  </div>
                </div>
            <?php endforeach; ?>
        </div>
        </div>
        <!-- Fixed Bottom Navigation -->
        <nav class="nav nav-pills nav-fill fixed-bottom bottom-nav-green shadow-lg" style="z-index:1030;">
            <a class="nav-link" href="dashboard.php"><span class="bi bi-house-door"></span> Home</a>
            <a class="nav-link" href="products.php"><span class="bi bi-flower1"></span> My Crops</a>
            <a class="nav-link active" href="my_products.php"><span class="bi bi-bag"></span> Crops</a>
            <a class="nav-link" href="team.php"><span class="bi bi-people"></span> Network</a>
            <a class="nav-link" href="profile.php"><span class="bi bi-person"></span> Profile</a>
        </nav>
    </div>
    <style>
    .bottom-nav-green {
        background: #a5d6a7;
        border-top: 2px solid #66bb6a;
        padding-bottom: env(safe-area-inset-bottom, 0);
    }
    .bottom-nav-green .nav-link {
        color: #1b5e20;
        font-weight: 500;
        font-size: 1.1em;
        padding: 0.7em 0 0.5em 0;
        border-radius: 0;
        transition: background 0.2s, color 0.2s;
    }
    .bottom-nav-green .nav-link.active, .bottom-nav-green .nav-link:focus, .bottom-nav-green .nav-link:hover {
        background: #66bb6a;
        color: #fff;
    }
    .bottom-nav-green .nav-link span {
        display: block;
        font-size: 1.3em;
    }
    body { padding-bottom: 70px !important; }
    </style>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css">
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
