<?php
session_start();
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}
require_once '../includes/db.php';
if (!isset($_GET['id'])) {
    header('Location: users.php');
    exit;
}
$id = intval($_GET['id']);
$stmt = $pdo->prepare('SELECT * FROM users WHERE id = ?');
$stmt->execute([$id]);
$user = $stmt->fetch();
if (!$user) {
    header('Location: users.php');
    exit;
}
$wallet = $pdo->prepare('SELECT balance FROM wallets WHERE user_id = ?');
$wallet->execute([$id]);
$balance = $wallet->fetchColumn();
// Fetch user investments
$investments = $pdo->prepare('SELECT i.*, p.crop_name FROM investments i JOIN packages p ON i.package_id = p.id WHERE i.user_id = ? ORDER BY i.start_date DESC');
$investments->execute([$id]);
$investments = $investments->fetchAll();
// Fetch active crops
$active_crops = $pdo->prepare('SELECT i.*, p.crop_name, p.amount FROM investments i JOIN packages p ON i.package_id = p.id WHERE i.user_id = ? AND i.status = "active" ORDER BY i.start_date DESC');
$active_crops->execute([$id]);
$active_crops = $active_crops->fetchAll();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>View User - GreenHarvest Admin</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body { background: #e8f5e9; }
        .main-green { background: #a5d6a7 !important; color: #1b5e20 !important; }
        .card-crop { border: 1px solid #c8e6c9; border-radius: 12px; box-shadow: 0 2px 8px rgba(60,60,60,0.05); }
        .side-nav { min-height: 100vh; background: #a5d6a7; padding-top: 2em; }
        .side-nav a { color: #1b5e20; font-weight: 500; display: block; padding: 1em 1.5em; text-decoration: none; border-radius: 0 20px 20px 0; margin-bottom: 0.5em; }
        .side-nav a.active, .side-nav a:hover { background: #66bb6a; color: #fff; }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-2 side-nav">
                <h4 class="mb-4">🌿 Admin</h4>
                <a href="dashboard.php">Dashboard</a>
                <a href="investments.php">Approve Investments</a>
                <a href="withdrawals.php">Approve Withdrawals</a>
                <a href="packages.php">Crop Packages</a>
                <a href="users.php" class="active">Users</a>
                <a href="referrals.php">Referrals</a>
                <a href="announcements.php">Announcements</a>
                <a href="settings.php">Settings</a>
                <a href="logout.php" class="text-danger">Logout</a>
            </div>
            <div class="col-md-10 py-4">
                <h2 class="mb-4">View User</h2>
                <div class="card card-crop p-4 mb-4">
                    <h5><?php echo htmlspecialchars($user['name']); ?></h5>
                    <p><strong>Email:</strong> <?php echo htmlspecialchars($user['email']); ?></p>
                    <p><strong>Phone:</strong> <?php echo htmlspecialchars($user['phone']); ?></p>
                    <p><strong>Referral Code:</strong> <?php echo htmlspecialchars($user['referral_code']); ?></p>
                    <p><strong>Wallet Balance:</strong> GHS <?php echo number_format($balance,2); ?></p>
                    <a href="users.php" class="btn btn-secondary">Back to Users</a>
                </div>
                <div class="card card-crop p-4 mb-4">
                    <h6 class="mb-3"><i class="bi bi-flower1 me-1"></i>Active Crops</h6>
                    <?php if ($active_crops): ?>
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped">
                            <thead class="table-success">
                                <tr>
                                    <th>Crop</th>
                                    <th>Amount</th>
                                    <th>Daily Return</th>
                                    <th>Start</th>
                                    <th>End</th>
                                </tr>
                            </thead>
                            <tbody>
                            <?php foreach ($active_crops as $crop): ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($crop['crop_name']); ?></td>
                                    <td>GHS <?php echo number_format($crop['amount'],2); ?></td>
                                    <td>GHS <?php echo number_format($crop['daily_return'],2); ?></td>
                                    <td><?php echo htmlspecialchars($crop['start_date']); ?></td>
                                    <td><?php echo htmlspecialchars($crop['end_date']); ?></td>
                                </tr>
                            <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                    <?php else: ?>
                        <div class="alert alert-info mb-0">No active crops.</div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
