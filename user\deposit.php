<?php
session_start();
if (!isset($_SESSION['user_id'])) {
    header('Location: ../index.php');
    exit;
}
require_once '../includes/db.php';
$user_id = $_SESSION['user_id'];
$error = '';
$success = '';
// Get payment number from config
$pay_stmt = $pdo->prepare("SELECT config_value FROM platform_config WHERE config_key = 'payment_number'");
$pay_stmt->execute();
$payment_number = $pay_stmt->fetchColumn();

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $amount = floatval($_POST['amount']);
    $reference = trim($_POST['reference']);
    if ($amount < 10) {
        $error = 'Minimum deposit is GHS 10.';
    } elseif (!$reference) {
        $error = 'Please enter your payment reference.';
    } else {
        // Insert deposit request (pending)
        $stmt = $pdo->prepare('INSERT INTO deposits (user_id, amount, status, reference) VALUES (?, ?, "pending", ?)');
        $stmt->execute([$user_id, $amount, $reference]);
        $_SESSION['deposit_modal'] = 'Deposit request submitted! Awaiting admin approval.';
        header('Location: deposit.php');
        exit;
    }
}
$stmt = $pdo->prepare('SELECT balance FROM wallets WHERE user_id = ?');
$stmt->execute([$user_id]);
$wallet = $stmt->fetch();
$balance = $wallet ? $wallet['balance'] : 0;
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Deposit - GreenHarvest</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="../assets/css/style.css">
    <style>
        body { background: #e8f5e9; }
        .main-green { background: #a5d6a7 !important; color: #1b5e20 !important; }
        .btn-farm { background: #43a047; color: #fff; }
        .btn-farm:hover { background: #2e7d32; }
        .card-crop { border: 1px solid #c8e6c9; border-radius: 12px; box-shadow: 0 2px 8px rgba(60,60,60,0.05); }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg main-green mb-4">
        <div class="container-fluid">
            <a class="navbar-brand fw-bold" href="dashboard.php">🌿 GreenHarvest</a>
            <a href="logout.php" class="btn btn-sm btn-danger">Logout</a>
        </div>
    </nav>
    <div class="container">
        <h2 class="mb-4">Deposit to Harvest Wallet</h2>
        <?php if ($error): ?><div class="alert alert-danger"><?php echo $error; ?></div><?php endif; ?>
        <!-- Deposit Modal -->
        <?php if (!empty($_SESSION['deposit_modal'])): ?>
        <div class="modal fade" id="depositModal" tabindex="-1" aria-labelledby="depositModalLabel" aria-hidden="true">
          <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
              <div class="modal-header">
                <h5 class="modal-title" id="depositModalLabel">Deposit Request</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
              </div>
              <div class="modal-body">
                <?php echo htmlspecialchars($_SESSION['deposit_modal']); unset($_SESSION['deposit_modal']); ?>
              </div>
              <div class="modal-footer">
                <button type="button" class="btn btn-success" data-bs-dismiss="modal">OK</button>
              </div>
            </div>
          </div>
        </div>
        <script>
        document.addEventListener('DOMContentLoaded', function() {
            var myModal = new bootstrap.Modal(document.getElementById('depositModal'));
            myModal.show();
        });
        </script>
        <?php endif; ?>
        <div class="card card-crop p-4 mb-4">
            <h5>Current Balance: <span class="text-success">GHS <?php echo number_format($balance,2); ?></span></h5>
            <form method="post">
                <div class="mb-3">
                    <label for="amount" class="form-label">Deposit Amount (GHS)</label>
                    <input type="number" class="form-control" id="amount" name="amount" min="10" required>
                </div>
                <div class="mb-3">
                    <label for="reference" class="form-label">Payment Reference</label>
                    <input type="text" class="form-control" id="reference" name="reference" required>
                    <div class="form-text">After payment to <strong><?php echo htmlspecialchars($payment_number); ?></strong>, enter your payment reference here.</div>
                </div>
                <button type="submit" class="btn btn-farm">Deposit</button>
                <a href="dashboard.php" class="btn btn-secondary">Back</a>
            </form>
        </div>
    </div>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
