<?php
session_start();
if (!isset($_SESSION['user_id'])) {
    header('Location: ../index.php');
    exit;
}
require_once '../includes/db.php';
$user_id = $_SESSION['user_id'];
// Fetch user info
$stmt = $pdo->prepare('SELECT name, email, phone FROM users WHERE id = ?');
$stmt->execute([$user_id]);
$user = $stmt->fetch();
// Fetch wallet balance
$stmt = $pdo->prepare('SELECT balance FROM wallets WHERE user_id = ?');
$stmt->execute([$user_id]);
$wallet = $stmt->fetch();
$balance = $wallet ? $wallet['balance'] : 0;
// Fetch withdrawal history
$stmt = $pdo->prepare('SELECT * FROM withdrawals WHERE user_id = ? ORDER BY date_requested DESC');
$stmt->execute([$user_id]);
$withdrawals = $stmt->fetchAll();
$error = '';
$success = '';
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['withdraw'])) {
    $amount = floatval($_POST['amount']);
    if ($amount < 30) {
        $_SESSION['withdrawal_modal'] = 'Minimum withdrawal is GHS 30.';
    } elseif ($amount > $balance) {
        $_SESSION['withdrawal_modal'] = 'Insufficient balance.';
    } else {
        // Deduct immediately, mark as pending
        $pdo->prepare('UPDATE wallets SET balance = balance - ? WHERE user_id = ?')->execute([$amount, $user_id]);
        $stmt = $pdo->prepare('INSERT INTO withdrawals (user_id, amount, status) VALUES (?, ?, "pending")');
        $stmt->execute([$user_id, $amount]);
        $_SESSION['withdrawal_modal'] = 'Withdrawal request submitted! Awaiting admin approval.';
    }
    header('Location: profile.php');
    exit;
}
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_password'])) {
    $old = $_POST['old_password'];
    $new = $_POST['new_password'];
    $confirm = $_POST['confirm_password'];
    $stmt = $pdo->prepare('SELECT password FROM users WHERE id = ?');
    $stmt->execute([$user_id]);
    $row = $stmt->fetch();
    if (!password_verify($old, $row['password'])) {
        $error = 'Old password is incorrect.';
    } elseif ($new !== $confirm) {
        $error = 'New passwords do not match.';
    } else {
        $hashed = password_hash($new, PASSWORD_DEFAULT);
        $stmt = $pdo->prepare('UPDATE users SET password = ? WHERE id = ?');
        $stmt->execute([$hashed, $user_id]);
        $success = 'Password updated successfully!';
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Profile - GreenHarvest</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="../assets/css/style.css">
    <style>
        body { background: #e8f5e9; }
        .main-green { background: #a5d6a7 !important; color: #1b5e20 !important; }
        .btn-farm { background: #43a047; color: #fff; }
        .btn-farm:hover { background: #2e7d32; }
        .card-crop { border: 1px solid #c8e6c9; border-radius: 12px; box-shadow: 0 2px 8px rgba(60,60,60,0.05); }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg main-green mb-4">
        <div class="container-fluid">
            <a class="navbar-brand fw-bold" href="dashboard.php">🌿 GreenHarvest</a>
            <a href="logout.php" class="btn btn-sm btn-danger">Logout</a>
        </div>
    </nav>
    <div class="container">
        <h2 class="mb-4">Farmer Profile</h2>
        <?php if ($error): ?><div class="alert alert-danger"><?php echo $error; ?></div><?php endif; ?>
        <?php if ($success): ?><div class="alert alert-success"><?php echo $success; ?></div><?php endif; ?>
        <!-- Withdrawal Modal -->
        <?php if (!empty($_SESSION['withdrawal_modal'])): ?>
        <div class="modal fade" id="withdrawalModal" tabindex="-1" aria-labelledby="withdrawalModalLabel" aria-hidden="true">
          <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
              <div class="modal-header">
                <h5 class="modal-title" id="withdrawalModalLabel">Withdrawal Request</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
              </div>
              <div class="modal-body">
                <?php echo htmlspecialchars($_SESSION['withdrawal_modal']); unset($_SESSION['withdrawal_modal']); ?>
              </div>
              <div class="modal-footer">
                <button type="button" class="btn btn-success" data-bs-dismiss="modal">OK</button>
              </div>
            </div>
          </div>
        </div>
        <script>
        document.addEventListener('DOMContentLoaded', function() {
            var myModal = new bootstrap.Modal(document.getElementById('withdrawalModal'));
            myModal.show();
        });
        </script>
        <?php endif; ?>
        <div class="row mb-4">
            <div class="col-md-6 mb-3">
                <div class="card card-crop">
                    <div class="card-body">
                        <h6>Name</h6>
                        <p><?php echo htmlspecialchars($user['name']); ?></p>
                        <h6>Email</h6>
                        <p><?php echo htmlspecialchars($user['email']); ?></p>
                        <h6>Phone</h6>
                        <p><?php echo htmlspecialchars($user['phone']); ?></p>
                        <h6>Wallet Balance</h6>
                        <h4 class="text-success">GHS <?php echo number_format($balance,2); ?></h4>
                        <form method="post" class="mt-3">
                            <div class="mb-3">
                                <label for="amount" class="form-label">Withdraw Amount (GHS)</label>
                                <input type="number" class="form-control" id="amount" name="amount" min="30" max="<?php echo $balance; ?>" required>
                            </div>
                            <button type="submit" name="withdraw" class="btn btn-farm">Request Withdrawal</button>
                        </form>
                    </div>
                </div>
            </div>
            <div class="col-md-6 mb-3">
                <div class="card card-crop">
                    <div class="card-body">
                        <h6>Update Password</h6>
                        <form method="post">
                            <div class="mb-2">
                                <label for="old_password" class="form-label">Old Password</label>
                                <input type="password" class="form-control" id="old_password" name="old_password" required>
                            </div>
                            <div class="mb-2">
                                <label for="new_password" class="form-label">New Password</label>
                                <input type="password" class="form-control" id="new_password" name="new_password" required>
                            </div>
                            <div class="mb-2">
                                <label for="confirm_password" class="form-label">Confirm New Password</label>
                                <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                            </div>
                            <button type="submit" name="update_password" class="btn btn-farm">Update Password</button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
        <div class="row mb-4">
            <div class="col-12">
                <div class="card card-crop">
                    <div class="card-body">
                        <h6>Withdrawal History</h6>
                        <ul class="list-group list-group-flush">
                            <?php if ($withdrawals): foreach ($withdrawals as $w): ?>
                                <li class="list-group-item">
                                    GHS <?php echo number_format($w['amount'],2); ?> - <span class="badge bg-<?php echo $w['status']=='approved'?'success':($w['status']=='pending'?'warning':'danger'); ?>"><?php echo ucfirst($w['status']); ?></span> (<?php echo date('M d, Y', strtotime($w['date_requested'])); ?>)
                                </li>
                            <?php endforeach; else: ?>
                                <li class="list-group-item">No withdrawals yet.</li>
                            <?php endif; ?>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        </div>
        <!-- Fixed Bottom Navigation -->
        <nav class="nav nav-pills nav-fill fixed-bottom bottom-nav-green shadow-lg" style="z-index:1030;">
            <a class="nav-link" href="dashboard.php"><span class="bi bi-house-door"></span> Home</a>
            <a class="nav-link" href="products.php"><span class="bi bi-flower1"></span> My Crops</a>
            <a class="nav-link" href="my_products.php"><span class="bi bi-bag"></span> Crops</a>
            <a class="nav-link" href="team.php"><span class="bi bi-people"></span> Network</a>
            <a class="nav-link active" href="profile.php"><span class="bi bi-person"></span> Profile</a>
        </nav>
    </div>
    <style>
    .bottom-nav-green {
        background: #a5d6a7;
        border-top: 2px solid #66bb6a;
        padding-bottom: env(safe-area-inset-bottom, 0);
    }
    .bottom-nav-green .nav-link {
        color: #1b5e20;
        font-weight: 500;
        font-size: 1.1em;
        padding: 0.7em 0 0.5em 0;
        border-radius: 0;
        transition: background 0.2s, color 0.2s;
    }
    .bottom-nav-green .nav-link.active, .bottom-nav-green .nav-link:focus, .bottom-nav-green .nav-link:hover {
        background: #66bb6a;
        color: #fff;
    }
    .bottom-nav-green .nav-link span {
        display: block;
        font-size: 1.3em;
    }
    body { padding-bottom: 70px !important; }
    </style>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css">
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
