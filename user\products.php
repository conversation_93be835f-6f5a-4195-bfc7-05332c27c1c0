<?php
session_start();
if (!isset($_SESSION['user_id'])) {
    header('Location: ../index.php');
    exit;
}
require_once '../includes/db.php';
$user_id = $_SESSION['user_id'];
// Fetch wallet balance
$stmt = $pdo->prepare('SELECT balance FROM wallets WHERE user_id = ?');
$stmt->execute([$user_id]);
$wallet = $stmt->fetch();
$balance = $wallet ? $wallet['balance'] : 0;
// Fetch user's investments
$stmt = $pdo->prepare('SELECT i.*, p.crop_name, p.duration, p.total_return, p.amount, p.image FROM investments i JOIN packages p ON i.package_id = p.id WHERE i.user_id = ? ORDER BY i.start_date DESC');
$stmt->execute([$user_id]);
$investments = $stmt->fetchAll();

$total_expenses = 0;
$total_revenue = 0;
if ($investments) {
    foreach ($investments as $inv) {
        $total_expenses += $inv['amount'];
        $total_revenue += $inv['profit_so_far'];
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My Farms - GreenHarvest</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="../assets/css/style.css">
    <style>
        body { background: #e8f5e9; }
        .main-green { background: #a5d6a7 !important; color: #1b5e20 !important; }
        .btn-farm { background: #43a047; color: #fff; }
        .btn-farm:hover { background: #2e7d32; }
        .card-crop { border: 1px solid #c8e6c9; border-radius: 12px; box-shadow: 0 2px 8px rgba(60,60,60,0.05); }
        .progress-bar-farm { background: #66bb6a; }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg main-green mb-4">
        <div class="container-fluid">
            <a class="navbar-brand fw-bold" href="dashboard.php">🌿 GreenHarvest</a>
            <a href="logout.php" class="btn btn-sm btn-danger">Logout</a>
        </div>
    </nav>
    <div class="container">
        <h2 class="mb-4">My Farms</h2>
<div class="card card-crop mb-4">
    <div class="card-header bg-success text-white">My Assets</div>
    <div class="card-body">
        <div class="row align-items-center">
            <div class="col-6">
                <div class="fw-bold text-muted">Total Expenses</div>
                <div class="text-danger fw-bold"><h3>GHS <?php echo number_format($total_expenses,2); ?></h3></div>
            </div>
            <div class="col-6">
                <div class="fw-bold text-muted">Total Revenue</div>
                <div class="text-success fw-bold"><h3>GHS <?php echo number_format($total_revenue,2); ?></h3></div>
            </div>
        </div>
    </div>
</div>
<div class="row g-2">
    <?php if ($investments): foreach ($investments as $inv):
        $now = new DateTime();
        $start = new DateTime($inv['start_date']);
        $end = new DateTime($inv['end_date']);
        $total_days = $inv['duration'];
        $days_gone = $start > $now ? 0 : min($now->diff($start)->days, $total_days);
        $progress = $total_days > 0 ? min(100, round(($days_gone / $total_days) * 100)) : 0;
        $status = $inv['status'];
    ?>
    <div class="col-md-6 col-lg-4 mb-3">
        <div class="card card-crop h-100 p-2">
            <div class="row g-0 align-items-center">
                <div class="col-6">
                        <div class="fw-bold mb-1"><?php echo htmlspecialchars($inv['crop_name']); ?></div>
                    <div class="small text-muted mb-1">Start: <?php echo htmlspecialchars($inv['start_date']); ?></div>
                    <div class="small text-muted">End: <?php echo htmlspecialchars($inv['end_date']); ?></div>
                </div>

                <div class="col-6 text-end">
                    <?php $img_path = (!empty($inv['image'])) ? '../' . $inv['image'] : '../assets/images/default-crop.png'; ?>
                    
                    <img src="<?php echo $img_path; ?>" alt="<?php echo htmlspecialchars($inv['crop_name']); ?>" style="width:48px;height:48px;object-fit:cover;border-radius:8px;">
                    <div class="fw-bold">GHS <?php echo number_format($inv['amount'],2); ?></div>
                    <div class="small text-<?php echo $status=='active'?'success':'secondary'; ?>"><?php echo ucfirst($status); ?></div>
                </div>
            </div>
            <div class="mt-2">
                <div class="progress" style="height: 12px;">
                    <div class="progress-bar progress-bar-farm" role="progressbar" style="width: <?php echo $progress; ?>%;" aria-valuenow="<?php echo $progress; ?>" aria-valuemin="0" aria-valuemax="100"></div>
                </div>
                <small class="text-muted">Growth: <?php echo $days_gone; ?>/<?php echo $total_days; ?> days</small>
            </div>
        </div>
    </div>
    <?php endforeach; else: ?>
        <div class="col-12"><div class="alert alert-info">You have no active or completed farms yet.</div></div>
    <?php endif; ?>
</div>
        </div>
        <!-- Fixed Bottom Navigation -->
        <nav class="nav nav-pills nav-fill fixed-bottom bottom-nav-green shadow-lg" style="z-index:1030;">
            <a class="nav-link" href="dashboard.php"><span class="bi bi-house-door"></span> Home</a>
            <a class="nav-link active" href="products.php"><span class="bi bi-flower1"></span> My Crops</a>
            <a class="nav-link" href="my_products.php"><span class="bi bi-bag"></span> Crops</a>
            <a class="nav-link" href="team.php"><span class="bi bi-people"></span> Network</a>
            <a class="nav-link" href="profile.php"><span class="bi bi-person"></span> Profile</a>
        </nav>
    </div>
    <style>
    .bottom-nav-green {
        background: #a5d6a7;
        border-top: 2px solid #66bb6a;
        padding-bottom: env(safe-area-inset-bottom, 0);
    }
    .bottom-nav-green .nav-link {
        color: #1b5e20;
        font-weight: 500;
        font-size: 1.1em;
        padding: 0.7em 0 0.5em 0;
        border-radius: 0;
        transition: background 0.2s, color 0.2s;
    }
    .bottom-nav-green .nav-link.active, .bottom-nav-green .nav-link:focus, .bottom-nav-green .nav-link:hover {
        background: #66bb6a;
        color: #fff;
    }
    .bottom-nav-green .nav-link span {
        display: block;
        font-size: 1.3em;
    }
    body { padding-bottom: 70px !important; }
    </style>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css">
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
