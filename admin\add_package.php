<?php
session_start();
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}
require_once '../includes/db.php';
$error = '';
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $crop_name = trim($_POST['crop_name']);
    $amount = floatval($_POST['amount']);
    $daily_return = floatval($_POST['daily_return']);
    $duration = intval($_POST['duration']);
    $total_return = floatval($_POST['total_return']);
    $description = trim($_POST['description']);
    $image_path = null;
    if (isset($_FILES['image']) && $_FILES['image']['error'] === UPLOAD_ERR_OK) {
        $ext = pathinfo($_FILES['image']['name'], PATHINFO_EXTENSION);
        $image_path = 'assets/images/crops/' . uniqid('crop_', true) . '.' . $ext;
        if (!is_dir('../assets/images/crops')) {
            mkdir('../assets/images/crops', 0777, true);
        }
        move_uploaded_file($_FILES['image']['tmp_name'], '../' . $image_path);
    }
    if ($crop_name && $amount && $daily_return && $duration && $total_return && $description) {
        $stmt = $pdo->prepare('INSERT INTO packages (crop_name, amount, daily_return, duration, total_return, description, image) VALUES (?, ?, ?, ?, ?, ?, ?)');
        $stmt->execute([$crop_name, $amount, $daily_return, $duration, $total_return, $description, $image_path]);
        header('Location: packages.php');
        exit;
    } else {
        $error = 'Please fill in all fields.';
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Add Crop Package - GreenHarvest Admin</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body { background: #e8f5e9; }
        .main-green { background: #a5d6a7 !important; color: #1b5e20 !important; }
        .card-crop { border: 1px solid #c8e6c9; border-radius: 12px; box-shadow: 0 2px 8px rgba(60,60,60,0.05); }
        .side-nav { min-height: 100vh; background: #a5d6a7; padding-top: 2em; }
        .side-nav a { color: #1b5e20; font-weight: 500; display: block; padding: 1em 1.5em; text-decoration: none; border-radius: 0 20px 20px 0; margin-bottom: 0.5em; }
        .side-nav a.active, .side-nav a:hover { background: #66bb6a; color: #fff; }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-2 side-nav">
                <h4 class="mb-4">🌿 Admin</h4>
                <a href="dashboard.php">Dashboard</a>
                <a href="investments.php">Approve Investments</a>
                <a href="withdrawals.php">Approve Withdrawals</a>
                <a href="packages.php" class="active">Crop Packages</a>
                <a href="users.php">Users</a>
                <a href="referrals.php">Referrals</a>
                <a href="announcements.php">Announcements</a>
                <a href="settings.php">Settings</a>
                <a href="logout.php" class="text-danger">Logout</a>
            </div>
            <div class="col-md-10 py-4">
                <h2 class="mb-4">Add Crop Package</h2>
                <?php if ($error): ?><div class="alert alert-danger"><?php echo $error; ?></div><?php endif; ?>
                <form method="post" enctype="multipart/form-data" class="card card-crop p-4">
                    <div class="mb-3">
                        <label for="crop_name" class="form-label">Crop Name</label>
                        <input type="text" class="form-control" id="crop_name" name="crop_name" required>
                    </div>
                    <div class="mb-3">
                        <label for="amount" class="form-label">Investment Amount (GHS)</label>
                        <input type="number" step="0.01" class="form-control" id="amount" name="amount" required>
                    </div>
                    <div class="mb-3">
                        <label for="daily_return" class="form-label">Daily Return (GHS)</label>
                        <input type="number" step="0.01" class="form-control" id="daily_return" name="daily_return" required>
                    </div>
                    <div class="mb-3">
                        <label for="duration" class="form-label">Duration (days)</label>
                        <input type="number" class="form-control" id="duration" name="duration" required>
                    </div>
                    <div class="mb-3">
                        <label for="total_return" class="form-label">Total Return (GHS)</label>
                        <input type="number" step="0.01" class="form-control" id="total_return" name="total_return" required>
                    </div>
                    <div class="mb-3">
                        <label for="description" class="form-label">Description</label>
                        <textarea class="form-control" id="description" name="description" rows="3" required></textarea>
                    </div>
                    <div class="mb-3">
                        <label for="image" class="form-label">Crop Image</label>
                        <input type="file" class="form-control" id="image" name="image" accept="image/*">
                    </div>
                    <button type="submit" class="btn btn-success">Add Package</button>
                    <a href="packages.php" class="btn btn-secondary">Cancel</a>
                </form>
            </div>
        </div>
    </div>
</body>
</html>
