<?php
session_start();
if (isset($_SESSION['user_id'])) {
    header('Location: user/dashboard.php');
    exit;
}
require_once 'includes/db.php';

$error = '';
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $email = trim($_POST['email']);
    $password = $_POST['password'];
    if ($email && $password) {
        $stmt = $pdo->prepare('SELECT * FROM users WHERE email = ?');
        $stmt->execute([$email]);
        $user = $stmt->fetch();
        if ($user && password_verify($password, $user['password'])) {
            $_SESSION['user_id'] = $user['id'];
            header('Location: user/dashboard.php');
            exit;
        } else {
            $error = 'Invalid email or password.';
        }
    } else {
        $error = 'Please fill in all fields.';
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - GreenHarvest</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="assets/css/style.css">
    <style>
        body {
            background: #e8f5e9;
        }
        .main-green {
            background: #a5d6a7 !important;
            color: #1b5e20 !important;
        }
        .secondary-green {
            background: #66bb6a !important;
            color: #fff !important;
        }
        .btn-farm {
            background: #43a047;
            color: #fff;
        }
        .btn-farm:hover {
            background: #2e7d32;
        }
        .card-crop {
            border: 1px solid #c8e6c9;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(60,60,60,0.05);
        }
    </style>
</head>
<body>
    <!-- Loader Overlay -->
    <div id="loader-overlay" style="position:fixed;top:0;left:0;width:100vw;height:100vh;z-index:2000;background:rgba(255,255,255,0.85);display:flex;align-items:center;justify-content:center;">
        <div class="spinner-border text-success" style="width:3rem;height:3rem;" role="status">
            <span class="visually-hidden">Loading...</span>
        </div>
    </div>
    <script>
    window.addEventListener('load', function() {
        var loader = document.getElementById('loader-overlay');
        if(loader) loader.style.display = 'none';
    });
    </script>
    <div class="container py-5 d-flex align-items-center justify-content-center" style="min-height: 100vh;">
        <div class="row justify-content-center w-100">
            <div class="col-md-5">
                <div class="card card-crop p-4 main-green">
                    <h2 class="mb-3 text-center">User Login</h2>
                    <?php if ($error): ?>
                        <div class="alert alert-danger"><?php echo $error; ?></div>
                    <?php endif; ?>
                    <form method="post" autocomplete="off">
                        <div class="mb-3">
                            <label for="email" class="form-label">Email</label>
                            <input type="email" class="form-control" id="email" name="email" required>
                        </div>
                        <div class="mb-3">
                            <label for="password" class="form-label">Password</label>
                            <input type="password" class="form-control" id="password" name="password" required>
                        </div>
                        <button type="submit" class="btn btn-farm w-100">Login</button>
                    </form>
                    <div class="mt-3 text-center">
                        <a href="user/register.php" class="text-success">Don't have an account? Register</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
