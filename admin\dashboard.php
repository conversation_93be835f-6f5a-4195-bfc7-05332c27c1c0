<?php
session_start();
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}
require_once '../includes/db.php';
// Platform stats
$user_count = $pdo->query('SELECT COUNT(*) FROM users')->fetchColumn();
$investment_count = $pdo->query('SELECT COUNT(*) FROM investments')->fetchColumn();
$total_earnings = $pdo->query('SELECT SUM(amount) FROM transactions WHERE type = "investment" AND status = "approved"')->fetchColumn();
$pending_withdrawals = $pdo->query('SELECT COUNT(*) FROM withdrawals WHERE status = "pending"')->fetchColumn();
$pending_investments = $pdo->query('SELECT COUNT(*) FROM investments WHERE status = "pending"')->fetchColumn();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard - GreenHarvest</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body { background: #e8f5e9; }
        .main-green { background: #a5d6a7 !important; color: #1b5e20 !important; }
        .btn-farm { background: #43a047; color: #fff; }
        .btn-farm:hover { background: #2e7d32; }
        .card-crop { border: 1px solid #c8e6c9; border-radius: 12px; box-shadow: 0 2px 8px rgba(60,60,60,0.05); }
        .side-nav {
            min-height: 100vh;
            background: #a5d6a7;
            padding-top: 2em;
        }
        .side-nav a {
            color: #1b5e20;
            font-weight: 500;
            display: block;
            padding: 1em 1.5em;
            text-decoration: none;
            border-radius: 0 20px 20px 0;
            margin-bottom: 0.5em;
        }
        .side-nav a.active, .side-nav a:hover {
            background: #66bb6a;
            color: #fff;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-2 side-nav">
                <h4 class="mb-4">🌿 Admin</h4>
                <a href="dashboard.php" class="active">Dashboard</a>
                <a href="deposits.php">Approve Deposits</a>
                <a href="withdrawals.php">Approve Withdrawals</a>
                <a href="packages.php">Crop Packages</a>
                <a href="users.php">Users</a>
                <a href="referrals.php">Referrals</a>
                <a href="announcements.php">Announcements</a>
                <a href="platform_config.php">Payment Number</a>
                <a href="settings.php">Settings</a>
                <a href="logout.php" class="text-danger">Logout</a>
            </div>
            <div class="col-md-10 py-4">
                <h2 class="mb-4">Admin Dashboard</h2>
                <div class="row mb-4">
                    <div class="col-md-3 mb-3">
                        <div class="card card-crop">
                            <div class="card-body">
                                <h6>Total Users</h6>
                                <h3 class="text-success"><?php echo $user_count; ?></h3>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card card-crop">
                            <div class="card-body">
                                <h6>Total Investments</h6>
                                <h3 class="text-success"><?php echo $investment_count; ?></h3>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card card-crop">
                            <div class="card-body">
                                <h6>Total Earnings</h6>
                                <h3 class="text-success">GHS <?php echo number_format($total_earnings,2); ?></h3>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card card-crop">
                            <div class="card-body">
                                <h6>Pending Withdrawals</h6>
                                <h3 class="text-warning"><?php echo $pending_withdrawals; ?></h3>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row mb-4">
                    <div class="col-md-3 mb-3">
                        <div class="card card-crop">
                            <div class="card-body">
                                <h6>Pending Investments</h6>
                                <h3 class="text-warning"><?php echo $pending_investments; ?></h3>
                            </div>
                        </div>
                    </div>
                </div>
                <p>Welcome to the GreenHarvest admin dashboard. Use the menu to manage the platform.</p>
            </div>
        </div>
    </div>
</body>
</html>
